namespace :e_resources do
  desc "Sync e-resources from JSON file to database (with deletions)"
  task sync: :environment do
    puts "Starting e-resources sync..."
    
    
    unless File.exist?(json_file_path)
      puts "JSON file not found at #{json_file_path}"
      exit
    end
    
    begin
      # Read and parse JSON file
      file_content = File.read(json_file_path)
      json_resources = JSON.parse(file_content)
      
      # Get all current JSON resource identifiers
      json_identifiers = json_resources.map { |data| [data["title"], data["url"]] }
      
      created_count = 0
      updated_count = 0
      removed_count = 0
      
      # First, remove resources that are no longer in JSON
      puts "Checking for resources to remove..."
      EResource.all.each do |db_resource|
        identifier = [db_resource.title, db_resource.url]
        unless json_identifiers.include?(identifier)
          puts "Removing: #{db_resource.title}"
          db_resource.destroy
          removed_count += 1
        end
      end
      
      # Then sync resources from JSON
      puts "Syncing resources from JSON..."
      json_resources.each do |data|
        # Check if resource already exists
        existing_resource = EResource.find_by(title: data["title"], url: data["url"])
        
        if existing_resource
          # Update existing resource
          existing_resource.update!(
            discipline: data["discipline"],
            description: data["description"],
            tags: data["tags"],
            metadata: data["metadata"]
          )
          updated_count += 1
          puts "Updated: #{data['title']}"
        else
          # Create new resource
          EResource.create!(
            title: data["title"],
            url: data["url"],
            discipline: data["discipline"],
            description: data["description"],
            tags: data["tags"],
            metadata: data["metadata"]
          )
          created_count += 1
          puts "Created: #{data['title']}"
        end
      end
      
      puts "\n" + "="*50
      puts "Sync completed!"
      puts "Created: #{created_count} resources"
      puts "Updated: #{updated_count} resources"
      puts "Removed: #{removed_count} resources"
      puts "Total resources in database: #{EResource.count}"
      puts "="*50
      
    rescue JSON::ParserError => e
      puts "Error parsing JSON file: #{e.message}"
    rescue => e
      puts "Error syncing resources: #{e.message}"
    end
  end
  
  desc "Reset e-resources database from JSON file"
  task reset: :environment do
    puts "Resetting e-resources database..."
    
    # Clear existing records
    EResource.destroy_all
    puts "Cleared existing records"
    
    # Run the sync task
    Rake::Task['e_resources:sync'].invoke
  end
  
  desc "Show comparison between JSON and database"
  task compare: :environment do

    unless File.exist?(json_file_path)
      puts "JSON file not found at #{json_file_path}"
      exit
    end
    
    begin
      file_content = File.read(json_file_path)
      json_resources = JSON.parse(file_content)
      
      json_titles = json_resources.map { |r| r['title'] }.sort
      db_titles = EResource.pluck(:title).sort
      
      puts "JSON file has #{json_resources.count} resources"
      puts "Database has #{EResource.count} resources"
      puts
      
      # Resources in JSON but not in DB
      json_only = json_titles - db_titles
      if json_only.any?
        puts "Resources in JSON but NOT in database:"
        json_only.each { |title| puts "  - #{title}" }
        puts
      end
      
      # Resources in DB but not in JSON
      db_only = db_titles - json_titles
      if db_only.any?
        puts "Resources in database but NOT in JSON:"
        db_only.each { |title| puts "  - #{title}" }
        puts
      end
      
      if json_only.empty? && db_only.empty?
        puts "✅ JSON and database are in sync!"
      else
        puts "❌ JSON and database are out of sync. Run 'rails e_resources:sync' to fix."
      end
      
    rescue JSON::ParserError => e
      puts "Error parsing JSON file: #{e.message}"
    rescue => e
      puts "Error comparing resources: #{e.message}"
    end
  end
end
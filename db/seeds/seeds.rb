require 'json'

# Clear existing records if you want to start fresh
EResource.destroy_all

resources = JSON.parse(file)

resources.each do |data|
  EResource.create!(
    title: data["title"],
    url: data["url"],
    discipline: data["discipline"],
    description: data["description"],
    tags: data["tags"], # This should already be an array from JSON.parse
    metadata: data["metadata"] # This should already be a hash from JSON.parse
  )
end

puts "Created #{EResource.count} e-resources"
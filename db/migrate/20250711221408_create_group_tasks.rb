class CreateGroupTasks < ActiveRecord::Migration[7.1]
  tag :predeploy
  def change
    create_table :group_tasks do |t|
      t.references :group,           null: false, foreign_key: true, index: true
      t.string     :title,           null: false
      t.text       :description
      t.references :assigned_user,   foreign_key: { to_table: :users }
      t.datetime   :due_date
      t.datetime   :completed_at

      t.timestamps
    end

    add_index :group_tasks, :completed_at
  end
end

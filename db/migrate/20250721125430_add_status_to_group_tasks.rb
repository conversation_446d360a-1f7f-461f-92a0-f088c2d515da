class AddStatusToGroupTasks < ActiveRecord::Migration[7.1]
  tag :predeploy
  def change
    add_column :group_tasks, :status, :string, default: 'todo'
    add_index :group_tasks, :status
    
    # Update existing records to have proper status based on completed_at
    reversible do |dir|
      dir.up do
        execute <<-SQL
          UPDATE group_tasks 
          SET status = CASE 
            WHEN completed_at IS NOT NULL THEN 'done'
            ELSE 'todo'
          END
        SQL
      end
    end
  end
end

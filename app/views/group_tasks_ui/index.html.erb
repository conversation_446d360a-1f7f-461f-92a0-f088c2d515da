<% content_for :page_title, 'Tasks' %>

<div style="padding: 20px; min-height: 100vh;">

  <!-- Header Section -->
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
    <div>
      <%= link_to group_path(@group), style: "text-decoration: none; color: #0374B5; font-weight: 500; display: flex; align-items: center; gap: 8px;" do %>
        <span style="font-size: 18px;">←</span> Back to Home
      <% end %>
    </div>
    
    <div style="display: flex; align-items: center; gap: 15px;">
      <span id="task-count" style="color: #666; font-size: 14px;">
        <span style="font-weight: 600; color: #333;"><%= @tasks.count %></span> tasks
      </span>
      <button id="add-task-btn" style="background: #033f1d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-weight: 500; font-size: 14px; transition: all 0.2s ease;">
        + Add Task
      </button>
    </div>
  </div>

  <!-- Tasks Container -->
  <div style="max-width: 1200px;">
    <h2 style="margin: 0 0 20px 0; color: #333; font-size: 24px; font-weight: 600;">Group Tasks</h2>
    
    <div id="tasks-list">
      <% if @tasks.any? %>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; max-width: 100%;">
          <% @tasks.each do |task| %>
            <% 
              # Read status directly from the database - this is the key fix!
              current_status = task.status || 'todo'
              
              # Set styling based on actual saved status
              border_color = case current_status
                when 'done' then '#C0E6C0'
                when 'in_progress' then '#FFEB9C'
                when 'reopen' then '#FFDAB9'
                else '#F5F5F5'
              end
              
              dropdown_bg = case current_status
                when 'done' then '#C0E6C0'
                when 'in_progress' then '#FFEB9C'
                when 'reopen' then '#FFDAB9'
                else '#F5F5F5'
              end
              
              title_style = current_status == 'done' ? 'color: #666; text-decoration: line-through;' : 'color: #666;'
            %>
            
            <div class="task-row" style="
              background: white; 
              border-radius: 4px; 
              border: 1px solid #C7CDD1; 
              padding: 20px; 
              transition: all 0.2s ease;
              position: relative;
              overflow: hidden;
            " data-task-id="<%= task.id %>">
              
              <!-- Top Border Color -->
              <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: <%= border_color %>;"></div>
              
              <!-- Task Content -->
              <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                <h3 style="
                  margin: 0; 
                  font-size: 16px; 
                  font-weight: 600; 
                  text-decoration: none;
                  cursor: pointer;
                  <%= title_style %>
                ">
                  <%= task.title %>
                </h3>
                
                <!-- Status Dropdown and Menu -->
                <div style="display: flex; align-items: center; gap: 8px;">
                  <select class="status-dropdown" data-task-id="<%= task.id %>" style="
                    background: <%= dropdown_bg %>; 
                    color: black; 
                    border: none;
                    padding: 4px 8px; 
                    border-radius: 3px; 
                    font-size: 12px; 
                    font-weight: 600;
                    text-transform: uppercase;
                    cursor: pointer;
                    background-image: url('data:image/svg+xml;utf8,<svg fill=\"white\" height=\"24\" viewBox=\"0 0 24 24\" width=\"24\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M7 10l5 5 5-5z\"/></svg>');
                    background-repeat: no-repeat;
                    background-position: right 4px center;
                    background-size: 12px;
                    padding-right: 20px;
                  ">
                    <option value="todo" <%= 'selected' if current_status == 'todo' %> style="background: white; color: black;">TO DO</option>
                    <option value="in_progress" <%= 'selected' if current_status == 'in_progress' %> style="background: white; color: black;">IN PROGRESS</option>
                    <option value="done" <%= 'selected' if current_status == 'done' %> style="background: white; color: black;">DONE</option>
                    <option value="reopen" <%= 'selected' if current_status == 'reopen' %> style="background: white; color: black;">RE-OPEN</option>
                  </select>
                  
                  <!-- Three Dots Menu -->
                  <div style="position: relative;">
                    <button class="task-menu-btn" style="
                      background: none; 
                      border: none; 
                      padding: 4px; 
                      cursor: pointer; 
                      border-radius: 4px;
                      transition: background-color 0.2s;
                      font-size: 16px;
                      color: #666;
                      width: 24px;
                      height: 24px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                    " data-task-id="<%= task.id %>">
                      ⋯
                    </button>
                    
                    <div class="task-menu" style="
                      position: absolute;
                      top: 100%;
                      right: 0;
                      background: white;
                      border: 1px solid #e9ecef;
                      border-radius: 4px;
                      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                      min-width: 120px;
                      z-index: 100;
                      display: none;
                    " data-task-id="<%= task.id %>">
                      <button class="edit-task-btn" style="
                        width: 100%;
                        padding: 8px 12px;
                        background: none;
                        border: none;
                        text-align: left;
                        cursor: pointer;
                        font-size: 14px;
                        color: #333;
                        transition: background-color 0.2s;
                      " data-task-id="<%= task.id %>">
                        Edit
                      </button>
                      <button class="delete-task-btn" style="
                        width: 100%;
                        padding: 8px 12px;
                        background: none;
                        border: none;
                        text-align: left;
                        cursor: pointer;
                        font-size: 14px;
                        color: #dc3545;
                        transition: background-color 0.2s;
                        border-top: 1px solid #f1f3f4;
                      " data-task-id="<%= task.id %>">
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Task Description -->
              <div style="margin-bottom: 16px; font-size: 14px; color: #666; line-height: 1.4;">
                <%= task.description.present? ? task.description : "This is the description" %>
              </div>
              
              <!-- Assignment and Date Info -->
              <div style="display: flex; justify-content: flex-end; font-size: 12px;">
                <div style="text-align: right;">
                  <div style="color: #666; margin-bottom: 2px;">
                    Created <%= task.created_at.strftime('%B %d, %Y at %I:%M %p') %>
                  </div>
                </div>
              </div>

              <div style="display: flex; justify-content: space-between; align-items: center; font-size: 12px;">
                <div style="display: flex; align-items: center; gap: 8px;">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="#666" style="margin-top: 1px;">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                  </svg>
                  <span style="color: #666; font-size: 12px;">
                    Assigned to: <span style="color: #033f1d; font-weight: 700;"><%= task.assigned_user.present? ? task.assigned_user.name : "Unassigned" %></span>
                  </span>
                </div>
                
                <% if task.due_date.present? %>
                  <div style="color: red; font-weight: 600;">
                    Due: <%= task.due_date.strftime('%B %d, %Y at %I:%M %p') %>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <div id="empty-state" style="
          text-align: center; 
          padding: 60px 20px; 
          background: white; 
          border-radius: 4px; 
          max-width: 500px;
          margin: 0 auto;
          color: #666;
        ">
          <div style="font-size: 48px; margin-bottom: 20px; color: #ccc;">📋</div>
          <h3 style="margin-bottom: 10px; color: #333;">No tasks yet</h3>
          <p>Create your first task to get started with group collaboration</p>
          <button id="add-first-task-btn" style="background: #033f1d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 15px; font-weight: 500;">
            + Add First Task
          </button>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  let currentTaskId = null;
  let isEditing = false;
  
  // Group users data for the modal
  const groupUsers = <%= raw @users.map { |u| { id: u.id, name: u.name } }.to_json %>;
  
  function createModal() {
    const modal = document.createElement('div');
    modal.id = 'task-modal';
    modal.style.cssText = `
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
    `;
    
    modal.innerHTML = `
      <div style="background-color: white; margin: 5% auto; padding: 0; border-radius: 4px; width: 90%; max-width: 600px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border: 1px solid #C7CDD1;">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px 30px; border-bottom: 1px solid #eee; background: #fafafa;">
          <h3 id="modal-title" style="margin: 0; color: #333; font-size: 18px;">New Task</h3>
          <button id="close-modal" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999; padding: 0; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;">&times;</button>
        </div>
        
        <form id="task-form" style="padding: 30px;">
          <div style="margin-bottom: 25px;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333; font-size: 14px;">Title <span style="color: red;">*</label>
            <input type="text" id="task-title" name="title" required placeholder="Enter the task title" style="width: 100%; padding: 12px 16px; border: 1px solid #C7CDD1; border-radius: 4px; font-size: 14px; box-sizing: border-box; height: 44px; transition: border-color 0.2s;">
          </div>
          
          <div style="margin-bottom: 25px;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333; font-size: 14px;">Description</label>
            <textarea id="task-description" name="description" rows="4" placeholder="Describe what this task involves..." style="width: 100%; padding: 12px 16px; border: 1px solid #C7CDD1; border-radius: 4px; font-size: 14px; box-sizing: border-box; resize: vertical; min-height: 100px; transition: border-color 0.2s;"></textarea>
          </div>
          
          <div style="margin-bottom: 25px;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333; font-size: 14px;">Assign to</label>
            <select id="task-assigned-user" name="assigned_user_id" style="width: 100%; padding: 12px 16px; border: 1px solid #C7CDD1; border-radius: 4px; font-size: 14px; box-sizing: border-box; height: 44px; background-color: white; transition: border-color 0.2s;">
              <option value="">Select a team member</option>
              ${groupUsers.map(user => `<option value="${user.id}">${user.name}</option>`).join('')}
            </select>
          </div>
          
          <div style="margin-bottom: 25px;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333; font-size: 14px;">Due Date <span style="color: red;">*</label>
            <input type="datetime-local" id="task-due-date" name="due_date" style="width: 100%; padding: 12px 16px; border: 1px solid #C7CDD1; border-radius: 4px; font-size: 14px; box-sizing: border-box; height: 44px; transition: border-color 0.2s;">
          </div>
          
          <div style="display: flex; justify-content: flex-end; gap: 12px; padding-top: 25px; border-top: 1px solid #eee; margin-top: 30px;">
            <button type="button" id="cancel-task" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: 500; transition: background-color 0.2s; min-width: 100px;">
              Cancel
            </button>
            <button type="submit" id="save-task" style="background: #033f1d; color: white; border: none; padding: 12px 24px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: 500; transition: background-color 0.2s; min-width: 120px;">
              Save Task
            </button>
          </div>
        </form>
      </div>
    `;
    
    document.body.appendChild(modal);
    return modal;
  }
  
  function openModal(title = 'New Task') {
    let modal = document.getElementById('task-modal');
    if (!modal) {
      modal = createModal();
      setupModalEvents(modal);
    }
    
    const modalTitle = modal.querySelector('#modal-title');
    modalTitle.textContent = title;
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
  }
  
  function closeModal() {
    const modal = document.getElementById('task-modal');
    if (modal) {
      modal.style.display = 'none';
      document.body.style.overflow = 'auto';
      resetForm();
    }
  }
  
  function resetForm() {
    const form = document.getElementById('task-form');
    if (form) {
      form.reset();
      currentTaskId = null;
      isEditing = false;
      const saveBtn = document.getElementById('save-task');
      if (saveBtn) saveBtn.textContent = 'Save Task';
    }
  }
  
  function updateTaskCount() {
    const taskRows = document.querySelectorAll('.task-row');
    const count = taskRows.length;
    const taskCountElement = document.getElementById('task-count');
    if (taskCountElement) {
      taskCountElement.innerHTML = `<span style="font-weight: 600; color: #333;">${count}</span> tasks`;
    }
  }
  
  function showEmptyState() {
    const tasksList = document.getElementById('tasks-list');
    tasksList.innerHTML = `
      <div id="empty-state" style="
        text-align: center; 
        padding: 60px 20px; 
        background: white; 
        border-radius: 4px; 
        border: 1px solid #C7CDD1;
        max-width: 500px;
        margin: 0 auto;
        color: #666;
      ">
        <div style="font-size: 48px; margin-bottom: 20px; color: #ccc;">📋</div>
        <h3 style="margin-bottom: 10px; color: #333;">No tasks yet</h3>
        <p>Create your first task to get started with group collaboration</p>
        <button id="add-first-task-btn" style="background: #033f1d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 15px; font-weight: 500;">
          + Add First Task
        </button>
      </div>
    `;
    
    // Re-attach event listener for the new button
    const addFirstTaskBtn = document.getElementById('add-first-task-btn');
    if (addFirstTaskBtn) {
      addFirstTaskBtn.addEventListener('click', () => openModal());
    }
  }
  
  function deleteTaskFromUI(taskId) {
    // Find the task row by task ID
    const taskRow = document.querySelector(`.task-row[data-task-id="${taskId}"]`);
    
    if (taskRow) {
      // Add visual feedback
      taskRow.style.opacity = '0.3';
      taskRow.style.transition = 'opacity 0.3s ease';
      
      // Remove after animation
      setTimeout(() => {
        taskRow.remove();
        
        // Update task count
        updateTaskCount();
        
        // Check if no tasks left
        const remainingTasks = document.querySelectorAll('.task-row');
        
        if (remainingTasks.length === 0) {
          showEmptyState();
        }
      }, 300);
      
      return true;
    }
    
    return false;
  }
  
  function setupModalEvents(modal) {
    const closeBtn = modal.querySelector('#close-modal');
    const cancelBtn = modal.querySelector('#cancel-task');
    const form = modal.querySelector('#task-form');
    
    closeBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(event) {
      if (event.target === modal) {
        closeModal();
      }
    });
    
  // Form submission - UPDATED to submit traditionally for redirects
    form.addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Validate due date before submission
      const dueDateInput = document.getElementById('task-due-date');
      if (dueDateInput && dueDateInput.value) {
        const selectedDate = new Date(dueDateInput.value);
        const now = new Date();
        
        // Add a small buffer (1 minute) to account for processing time
        const minimumDate = new Date(now.getTime() + (1 * 60 * 1000));
        
        if (selectedDate <= minimumDate) {
          alert('Due date must be in the future (at least 1 minute from now).');
          dueDateInput.focus();
          return;
        }
      }
      
      // Disable the submit button to prevent double submission
      const submitButton = form.querySelector('button[type="submit"]');
      const originalText = submitButton.textContent;
      submitButton.disabled = true;
      submitButton.textContent = isEditing ? 'Updating...' : 'Creating...';
      
      // Create a traditional form submission
      const hiddenForm = document.createElement('form');
      hiddenForm.method = 'POST';
      hiddenForm.style.display = 'none';
      
      if (isEditing) {
        hiddenForm.action = `/api/v1/group_tasks/${currentTaskId}`;
        // Add method override for PUT
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'PUT';
        hiddenForm.appendChild(methodInput);
      } else {
        hiddenForm.action = `/api/v1/groups/<%= @group.id %>/tasks`;
      }
      
      // Add CSRF token
      const csrfInput = document.createElement('input');
      csrfInput.type = 'hidden';
      csrfInput.name = 'authenticity_token';
      csrfInput.value = document.querySelector('[name="csrf-token"]').content;
      hiddenForm.appendChild(csrfInput);
      
      // Add form data
      const formData = new FormData(form);
      
      // Title
      const titleInput = document.createElement('input');
      titleInput.type = 'hidden';
      titleInput.name = 'group_task[title]';
      titleInput.value = formData.get('title');
      hiddenForm.appendChild(titleInput);
      
      // Description
      const descInput = document.createElement('input');
      descInput.type = 'hidden';
      descInput.name = 'group_task[description]';
      descInput.value = formData.get('description') || '';
      hiddenForm.appendChild(descInput);
      
      // Assigned User ID
      if (formData.get('assigned_user_id')) {
        const assignedInput = document.createElement('input');
        assignedInput.type = 'hidden';
        assignedInput.name = 'group_task[assigned_user_id]';
        assignedInput.value = formData.get('assigned_user_id');
        hiddenForm.appendChild(assignedInput);
      }
      
      // Due Date
      if (formData.get('due_date')) {
        const dueDateInput = document.createElement('input');
        dueDateInput.type = 'hidden';
        dueDateInput.name = 'group_task[due_date]';
        dueDateInput.value = formData.get('due_date');
        hiddenForm.appendChild(dueDateInput);
      }
      
      // Append to body and submit
      document.body.appendChild(hiddenForm);
      hiddenForm.submit();
    });
  }
  
  // Event listeners for add task buttons
  const addTaskBtn = document.getElementById('add-task-btn');
  const addFirstTaskBtn = document.getElementById('add-first-task-btn');
  
  if (addTaskBtn) {
    addTaskBtn.addEventListener('click', () => openModal());
  }
  
  if (addFirstTaskBtn) {
    addFirstTaskBtn.addEventListener('click', () => openModal());
  }
  
  // Status dropdown change - UPDATED to handle redirects
  document.addEventListener('change', function(e) {
    if (e.target.classList.contains('status-dropdown')) {
      const taskId = e.target.getAttribute('data-task-id');
      const newStatus = e.target.value;
      
      let updateData = {
        group_task: {
          status: newStatus
        }
      };
      
      // If marking as done, also set completed_at
      if (newStatus === 'done') {
        updateData.group_task.completed_at = new Date().toISOString();
      } else {
        // If not done, clear completed_at
        updateData.group_task.completed_at = null;
      }
      
      fetch(`/api/v1/group_tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify(updateData)
      })
      .then(response => {
        if (response.redirected || response.type === 'opaqueredirect' || response.ok) {
          // Reload to see flash message and updated status
          window.location.reload();
        } else {
          throw new Error('Network response was not ok');
        }
      })
      .catch(error => {
        // Reset dropdown to previous value on error
        location.reload();
      });
    }
  });
  
  // Edit task
  document.addEventListener('click', function(e) {
    if (e.target.classList.contains('edit-task-btn')) {
      const taskId = e.target.getAttribute('data-task-id');
      
      fetch(`/api/v1/group_tasks/${taskId}`)
        .then(response => {
          if (!response.ok) {
            throw new Error('Failed to fetch task data');
          }
          return response.json();
        })
        .then(data => {
          // Handle different possible response structures
          const task = data.group_task || data.task || data;
          
          currentTaskId = taskId;
          isEditing = true;
          
          // First open the modal
          openModal('Edit Task');
          
          // Use setTimeout to ensure modal DOM is ready
          setTimeout(() => {
            const titleInput = document.getElementById('task-title');
            const descInput = document.getElementById('task-description');
            const assignedSelect = document.getElementById('task-assigned-user');
            const dueDateInput = document.getElementById('task-due-date');
            const saveBtn = document.getElementById('save-task');
            
            // Populate form fields with multiple fallback options
            if (titleInput) {
              const title = task.title || task.name || '';
              titleInput.value = title;
            }
            if (descInput) {
              const description = task.description || task.desc || '';
              descInput.value = description;
            }
            if (assignedSelect) {
              const assignedUserId = task.assigned_user_id || task.assignedUserId || task.assigned_to || '';
              assignedSelect.value = assignedUserId;
            }
            
            // Handle due date formatting
            const dueDate = task.due_date || task.dueDate || task.due_at;
            if (dueDateInput && dueDate) {
              try {
                let dueDateObj;
                
                if (typeof dueDate === 'string') {
                  // Manual parsing to avoid timezone issues
                  const dateMatch = dueDate.match(/(\d{4})-(\d{2})-(\d{2})\s*(?:T|\s)(\d{2}):(\d{2}):?(\d{2})?/);
                  if (dateMatch) {
                    const [, year, month, day, hour, minute, second] = dateMatch;
                    dueDateObj = new Date(
                      parseInt(year), 
                      parseInt(month) - 1, // months are 0-indexed
                      parseInt(day), 
                      parseInt(hour), 
                      parseInt(minute), 
                      parseInt(second || 0)
                    );
                  } else {
                    dueDateObj = new Date(dueDate);
                  }
                } else {
                  dueDateObj = new Date(dueDate);
                }
                
                if (!isNaN(dueDateObj.getTime())) {
                  // Format for datetime-local input (YYYY-MM-DDTHH:mm)
                  const year = dueDateObj.getFullYear();
                  const month = String(dueDateObj.getMonth() + 1).padStart(2, '0');
                  const day = String(dueDateObj.getDate()).padStart(2, '0');
                  const hours = String(dueDateObj.getHours()).padStart(2, '0');
                  const minutes = String(dueDateObj.getMinutes()).padStart(2, '0');
                  const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
                  
                  dueDateInput.value = formattedDate;
                }
              } catch (error) {
                // Silent error handling for date parsing
              }
            }
            
            if (saveBtn) saveBtn.textContent = 'Update Task';
          }, 100);
        })
        .catch(error => {
          alert('Error loading task data: ' + error.message);
        });
    }
  });
  
  // Delete task
  document.addEventListener('click', function(e) {
    if (e.target.classList.contains('delete-task-btn')) {
      const taskId = e.target.getAttribute('data-task-id');
      
      if (confirm('Are you sure you want to delete this task?')) {
        // Disable the button immediately
        const deleteBtn = e.target;
        const originalText = deleteBtn.textContent;
        deleteBtn.disabled = true;
        deleteBtn.textContent = 'Deleting...';
        deleteBtn.style.opacity = '0.6';
        
        fetch(`/api/v1/group_tasks/${taskId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
            'Accept': 'application/json'
          }
        })
        .then(response => {
          if (response.redirected || response.type === 'opaqueredirect' || 
              response.ok || response.status === 204 || response.status === 200) {
            // Reload to see flash message
            window.location.reload();
          } else {
            throw new Error(`Delete failed with status ${response.status}: ${response.statusText}`);
          }
        })
        .catch(error => {
          // Re-enable button on error
          deleteBtn.disabled = false;
          deleteBtn.textContent = originalText;
          deleteBtn.style.opacity = '1';
          
          alert('Error deleting task: ' + error.message);
        });
      }
    }
  });

  // Add hover effects to task cards and menu functionality
  document.querySelectorAll('.task-row').forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
      this.style.transform = 'translateY(-1px)';
    });
    
    card.addEventListener('mouseleave', function() {
      this.style.boxShadow = 'none';
      this.style.transform = 'translateY(0)';
    });
  });

  // Three dots menu functionality
  document.addEventListener('click', function(e) {
    // Close all menus when clicking outside
    if (!e.target.closest('.task-menu-btn') && !e.target.closest('.task-menu')) {
      document.querySelectorAll('.task-menu').forEach(menu => {
        menu.style.display = 'none';
      });
    }

    // Toggle menu when clicking three dots
    if (e.target.classList.contains('task-menu-btn')) {
      e.stopPropagation();
      const taskId = e.target.getAttribute('data-task-id');
      const menu = document.querySelector(`.task-menu[data-task-id="${taskId}"]`);
      
      // Close all other menus
      document.querySelectorAll('.task-menu').forEach(m => {
        if (m !== menu) m.style.display = 'none';
      });
      
      // Toggle current menu
      menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
    }
  });

  // Add hover effects to menu buttons
  document.querySelectorAll('.task-menu-btn').forEach(btn => {
    btn.addEventListener('mouseenter', function() {
      this.style.backgroundColor = '#f1f3f4';
    });
    
    btn.addEventListener('mouseleave', function() {
      this.style.backgroundColor = 'transparent';
    });
  });

  // Add hover effects to menu items
  document.querySelectorAll('.edit-task-btn, .delete-task-btn').forEach(btn => {
    btn.addEventListener('mouseenter', function() {
      this.style.backgroundColor = '#f8f9fa';
    });
    
    btn.addEventListener('mouseleave', function() {
      this.style.backgroundColor = 'transparent';
    });
  });
});
</script>

<style>
select.status-dropdown {
  width: 102px;
}

@media (max-width: 768px) {
  div[style*="grid-template-columns"] {
    grid-template-columns: 1fr !important;
  }
}

/* Input focus styles to match Canvas */
input:focus, textarea:focus, select:focus {
  outline: none !important;
}

/* Button hover effects */
button:hover {
  opacity: 0.9 !important;
}

/* Status dropdown styling */
.status-dropdown option {
  background: white !important;
  color: black !important;
}

.status-dropdown:focus {
}
</style>
class GroupTasksUiController < ApplicationController
  before_action :require_user
  before_action :get_context
  before_action :set_group
  before_action :authorize_membership!
  
  def index
    @tasks = @group.group_tasks.preload(:assigned_user).order(:due_date)
    @users = @group.users
    
    # Set the context for the group layout - this is key for the sidebar
    @context = @group
    
    # Add breadcrumbs following <PERSON><PERSON> pattern
    if @group&.context
      add_crumb @group.context.short_name, named_context_url(@group.context, :context_url)
      add_crumb @group.short_name, named_context_url(@group, :context_url)
    elsif @group
      add_crumb @group.short_name, named_context_url(@group, :context_url)
    end
    add_crumb 'Tasks'
    set_active_tab 'tasks'
    
    respond_to do |format|
      format.html { render :index }
      format.json { render json: @tasks }
    end
  end
  
  private
  
  def get_context
    # This method is used by Canvas to set up the context
    if params[:group_id]
      @context = Group.find(params[:group_id])
    end
  end
  
  def set_group
    @group = @context if @context.is_a?(Group)
    @group ||= Group.find(params[:group_id])
  end
  
  def authorize_membership!
    unless @current_user && @group.users.include?(@current_user)
      flash[:error] = "You are not authorized to access this group"
      redirect_to root_path
    end
  end
end
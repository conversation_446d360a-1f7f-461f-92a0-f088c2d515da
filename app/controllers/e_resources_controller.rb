class EResourcesController < ApplicationController
  before_action :set_e_resource, only: [:show, :edit, :update, :destroy]
  before_action :require_admin, only: [:new, :create, :edit, :update, :destroy, :sync_from_json, :export_to_json]

  def index
    @current_nav = 'e-resources'
   
    # DON'T sync from JSON automatically - only do this on manual sync
    # sync_json_to_database
   
    # Get all resources from the database (ordered for display)
    resources = EResource.all.order(:discipline, :title)
   
    # Convert to the format your view expects
    resources_array = resources.map do |resource|
      {
        "title" => resource.title,
        "url" => resource.url,
        "discipline" => resource.discipline,
        "description" => resource.description,
        "tags" => resource.tags || [],
        "metadata" => resource.metadata || {}
      }
    end
   
    # Group by discipline
    @grouped_resources = resources_array.group_by { |res| res["discipline"] }
   
    # Admin-specific data (only loaded if user is admin)
    if current_user_is_admin?
      @e_resources = resources
      @disciplines = EResource.distinct.pluck(:discipline).sort
      @total_count = EResource.count
      # Fix: Use a separate query without ordering for grouping
      @discipline_counts = EResource.group(:discipline).count
      @is_admin = true
    else
      @is_admin = false
    end

    # Add JSON response support
    respond_to do |format|
      format.html # normal page view
      format.json { render json: resources_array }
    end
  end

  def show
    respond_to do |format|
      format.html { redirect_to e_resources_path }
      format.json {
        render json: {
          id: @e_resource.id,
          title: @e_resource.title,
          url: @e_resource.url,
          discipline: @e_resource.discipline,
          description: @e_resource.description,
          tags: @e_resource.tags || [],
          metadata: @e_resource.metadata || {}
        }
      }
    end
  end

  def new
    redirect_to e_resources_path
  end

  def create
    @e_resource = EResource.new(e_resource_params)
    
    if @e_resource.save
      # Update the JSON file after successful database save
      result = EResourcesSyncService.update_json_file
      if result[:success]
        flash[:notice] = 'E-Resource was successfully created.'
      else
        flash[:notice] = 'E-Resource was successfully created.'
        Rails.logger.error "JSON update failed: #{result[:message]}"
      end
      redirect_to e_resources_path
    else
      # For modal errors, redirect with error message
      flash[:error] = "Error creating resource: #{@e_resource.errors.full_messages.join(', ')}"
      redirect_to e_resources_path
    end
  end

  def edit
    redirect_to e_resources_path
  end

  def update
    if @e_resource.update(e_resource_params)
      # Update the JSON file after successful database update
      result = EResourcesSyncService.update_json_file
      if result[:success]
        flash[:notice] = 'E-Resource was successfully updated.'
      else
        flash[:notice] = 'E-Resource was successfully updated.'
        Rails.logger.error "JSON update failed: #{result[:message]}"
      end
      redirect_to e_resources_path
    else
      # For modal errors, redirect with error message
      flash[:error] = "Error updating resource: #{@e_resource.errors.full_messages.join(', ')}"
      redirect_to e_resources_path
    end
  end

  def destroy
    @e_resource.destroy
    # Update the JSON file after successful database deletion
    result = EResourcesSyncService.update_json_file
    if result[:success]
      flash[:notice] = 'E-Resource was successfully deleted.'
    else
      flash[:notice] = 'E-Resource was successfully deleted.'
      Rails.logger.error "JSON update failed: #{result[:message]}"
    end
    redirect_to e_resources_path
  end

  def update_json_file_action
    Rails.logger.info "Manual JSON file update requested"
    
    result = EResourcesSyncService.update_json_file
    
    if result[:success]
      flash[:notice] = "JSON file updated successfully with #{result[:count]} resources."
    else
      flash[:error] = "Failed to update JSON file: #{result[:message]}"
    end
    
    redirect_to e_resources_path
  end

  require 'csv'

  def export_to_csv
    resources = EResource.all

    csv_data = CSV.generate(headers: true) do |csv|
      # Define the headers
      csv << ["Title", "URL", "Discipline", "Description", "Tags", "Metadata"]

      # Add each resource as a row
      resources.each do |resource|
        csv << [
          resource.title,
          resource.url,
          resource.discipline,
          resource.description,
          (resource.tags || []).join(', '),
          resource.metadata.to_json # Convert metadata to JSON string for CSV
        ]
      end
    end

    # Send the CSV file as a response
    send_data csv_data,
              filename: "e_resources_#{Date.current.strftime('%Y%m%d')}.csv",
              type: 'text/csv',
              disposition: 'attachment'
  end

  private

  def current_user_is_admin?
    return false unless @current_user
   
    # Check if user is account admin or site admin
    @current_user.adminable_accounts.present? ||
    Account.site_admin.account_users.where(user: @current_user).exists?
  end

  def require_admin
    unless current_user_is_admin?
      flash[:error] = 'Access denied. Administrator privileges required.'
      redirect_to e_resources_path
      return false
    end
  end

  def set_e_resource
    @e_resource = EResource.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    flash[:error] = 'E-Resource not found.'
    redirect_to e_resources_path
  end

  def sync_json_to_database
   
    # Check if JSON file exists
    return unless File.exist?(json_file_path)

    begin
      # Read and parse JSON file
      file_content = File.read(json_file_path)
      json_resources = JSON.parse(file_content)
      
      # Get all current JSON resource identifiers
      json_identifiers = json_resources.map { |data| [data["title"], data["url"]] }
      
      removed_count = 0
      updated_count = 0
      created_count = 0
      
      # First, remove resources that are no longer in JSON
      EResource.all.each do |db_resource|
        identifier = [db_resource.title, db_resource.url]
        unless json_identifiers.include?(identifier)
          db_resource.destroy
          removed_count += 1
        end
      end
      
      # Then sync resources from JSON
      json_resources.each do |data|
        existing_resource = EResource.find_by(title: data["title"], url: data["url"])
        
        if existing_resource
          existing_resource.update!(
            discipline: data["discipline"],
            description: data["description"],
            tags: data["tags"],
            metadata: data["metadata"]
          )
          updated_count += 1
        else
          EResource.create!(
            title: data["title"],
            url: data["url"],
            discipline: data["discipline"],
            description: data["description"],
            tags: data["tags"],
            metadata: data["metadata"]
          )
          created_count += 1
        end
      end
      
      Rails.logger.info "Sync completed: #{created_count} created, #{updated_count} updated, #{removed_count} removed"
      
    rescue JSON::ParserError => e
      Rails.logger.error "JSON parsing error: #{e.message}"
    rescue => e
      Rails.logger.error "Error syncing resources: #{e.message}"
    end
  end
 
  def e_resource_params
    params.require(:e_resource).permit(
      :title, :url, :discipline, :description,
      tags: [],
      metadata: {}
    ).tap do |whitelisted|
      # Handle tags properly
      if params[:e_resource][:tags].is_a?(String)
        whitelisted[:tags] = params[:e_resource][:tags].split(',').map(&:strip).reject(&:blank?)
      end
     
      # Handle metadata
      if params[:e_resource][:metadata].present?
        whitelisted[:metadata] = {
          type: params[:e_resource][:metadata][:type],
          access: params[:e_resource][:metadata][:access],
          updated: params[:e_resource][:metadata][:updated]
        }.compact
      end
    end
  end
end
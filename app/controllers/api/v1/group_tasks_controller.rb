module Api
  module V1
    class GroupTasksController < ApplicationController
      before_action :set_group, only: [:index, :create]
      before_action :set_group_task, only: [:show, :update, :destroy, :complete]

      # GET http://localhost:3000/api/v1/groups/:group_id/tasks
      def index
        tasks = @group.group_tasks.order(:due_date)
        render json: tasks.map { |task| task_json(task) }, status: :ok
      end

      # POST http://localhost:3000/api/v1/groups/:group_id/tasks
      def create
        task = @group.group_tasks.new(task_params)
        
        if task.save
          flash[:notice] = 'Task was successfully created.'
          redirect_to group_tasks_path(@group)
        else
          flash[:error] = "Error creating task: #{task.errors.full_messages.join(', ')}"
          redirect_to group_tasks_path(@group)
        end
      rescue ActiveRecord::RecordInvalid => e
        flash[:error] = "Error creating task: #{e.record.errors.full_messages.join(', ')}"
        redirect_to group_tasks_path(@group)
      end

      # GET http://localhost:3000/api/v1/group_tasks/:id
      def show
        render json: { group_task: task_json(@group_task) }
      end

      # PUT http://localhost:3000/api/v1/group_tasks/:id
      def update
        if @group_task.update(task_params)
          flash[:notice] = 'Task was successfully updated.'
          redirect_to group_tasks_path(@group)
        else
          flash[:error] = "Error updating task: #{@group_task.errors.full_messages.join(', ')}"
          redirect_to group_tasks_path(@group)
        end
      end

      # DELETE http://localhost:3000/api/v1/group_tasks/:id
      def destroy
        if @group_task.destroy
          flash[:notice] = 'Task was successfully deleted.'
        else
          flash[:error] = "Error deleting task: #{@group_task.errors.full_messages.join(', ')}"
        end
        redirect_to group_tasks_path(@group)
      end

      # PUT http://localhost:3000/api/v1/group_tasks/:id/complete
      def complete
        @group_task.complete!
        render json: { group_task: task_json(@group_task) }
      end

      private

      def set_group
        @group = ::Group.find(params[:group_id])
      end

      def set_group_task
        @group_task = GroupTask.find(params[:id])
        @group = @group_task.group
      end

      def task_params
        params.require(:group_task).permit(
          :title, :description, :assigned_user_id, :due_date, :completed_at, :status
        )
      end

      def authorize_membership!
        unless @group.users.include?(current_user)
          render json: { error: "Forbidden" }, status: :forbidden
        end
      end

      # Helper method to format task JSON response consistently
      def task_json(task)
        {
          id: task.id,
          title: task.title,
          description: task.description,
          status: task.status,
          completed_at: task.completed_at,
          assigned_user_id: task.assigned_user_id,
          assigned_user: task.assigned_user&.name,
          due_date: task.due_date&.iso8601, # Format as ISO string to preserve timezone
          created_at: task.created_at,
          updated_at: task.updated_at,
          # Additional status helpers for frontend
          status_display: task.status_display,
          status_color: task.status_color,
          status_background_color: task.status_background_color,
          completed: task.completed?
        }
      end
    end
  end
end
class GroupTask < ApplicationRecord
  belongs_to :group
  belongs_to :assigned_user, class_name: 'User', optional: true
  
  validates :title, presence: true
  validates :status, inclusion: { in: %w[todo in_progress done reopen] }
  validate :completed_at_consistency
  
  # Set default status
  after_initialize :set_default_status, if: :new_record?
  
  # Add notification callbacks for all CRUD operations
  after_create :send_creation_notifications_async
  after_update :send_update_notifications_async
  after_destroy :send_deletion_notifications_async
  
  # Scopes for filtering by status
  scope :todo, -> { where(status: 'todo') }
  scope :in_progress, -> { where(status: 'in_progress') }
  scope :done, -> { where(status: 'done') }
  scope :reopen, -> { where(status: 'reopen') }
  scope :pending, -> { where(status: ['todo', 'in_progress', 'reopen']) }
  
  # Override status setter to maintain completed_at consistency
  def status=(new_status)
    super(new_status)
    
    case new_status
    when 'done'
      self.completed_at = Time.current if completed_at.blank?
    when 'todo', 'in_progress', 'reopen'
      self.completed_at = nil
    end
  end
  
  # Legacy method - kept for backward compatibility
  def complete!
    update!(status: 'done', completed_at: Time.current)
  end
  
  # Fixed method to check if task is completed - use AND logic for consistency
  def completed?
    status == 'done' && completed_at.present?
  end
  
  # New status management methods
  def mark_complete!
    update!(status: 'done', completed_at: Time.current)
  end
  
  def reopen!
    update!(status: 'reopen', completed_at: nil)
  end
  
  def mark_in_progress!
    update!(status: 'in_progress', completed_at: nil)
  end
  
  def mark_todo!
    update!(status: 'todo', completed_at: nil)
  end
  
  # Status display methods
  def status_display
    case status
    when 'todo' then 'TO DO'
    when 'in_progress' then 'IN PROGRESS'
    when 'done' then 'DONE'
    when 'reopen' then 'RE-OPEN'
    else status&.humanize
    end
  end
  
  def status_color
    case status
    when 'todo' then '#F5F5F5'
    when 'in_progress' then '#FFEB9C'
    when 'done' then '#C0E6C0'
    when 'reopen' then '#FFDAB9'
    else '#F5F5F5'
    end
  end
  
  def send_creation_notifications_async
    task_data = {
      id: self.id,
      title: self.title,
      assigned_user_id: self.assigned_user_id,
      group_id: self.group_id,
      completed: self.completed?,
      description: self.description,
      due_date: self.due_date
    }
    
    TaskNotificationJob.perform_later('create', task_data)
  end

  def send_update_notifications_async
    # Keep the existing logic for preventing infinite loops
    return if @updating_status_completed_at
    
    if saved_change_to_completed_at? && !saved_change_to_status?
      @updating_status_completed_at = true
      begin
        if completed_at.present?
          update_column(:status, 'done') if status != 'done'
        else
          update_column(:status, 'todo') if status == 'done'
        end
      ensure
        @updating_status_completed_at = false
      end
      return
    end
    
    task_data = {
      id: self.id,
      title: self.title,
      assigned_user_id: self.assigned_user_id,
      group_id: self.group_id,
      completed: self.completed?,
      description: self.description,
      due_date: self.due_date,
      changes: {
        status: saved_change_to_status?,
        assigned_user_id: saved_change_to_assigned_user_id?,
        title: saved_change_to_title?,
        description: saved_change_to_description?,
        due_date: saved_change_to_due_date?
      },
      old_values: {
        status: saved_change_to_status? ? saved_change_to_status[0] : nil,
        assigned_user_id: saved_change_to_assigned_user_id? ? saved_change_to_assigned_user_id[0] : nil
      }
    }
    
    TaskNotificationJob.perform_later('update', task_data)
  end

  def send_deletion_notifications_async
    task_data = {
      id: self.id,
      title: self.title,
      assigned_user_id: self.assigned_user_id,
      group_id: self.group_id
    }
    
    TaskNotificationJob.perform_later('destroy', task_data)
  end

  private
  
  def set_default_status
    self.status ||= 'todo'
  end
  
  # Add validation to ensure completed_at consistency
  def completed_at_consistency
    if status == 'done' && completed_at.blank?
      errors.add(:completed_at, "must be present when status is done")
    elsif status != 'done' && completed_at.present?
      errors.add(:completed_at, "must be blank when status is not done")
    end
  end
  
  def send_creation_notifications
    Rails.logger.info "Task created: #{title}"
    
    if assigned_user.present?
      # Notify only the assigned user - EMAIL INSTEAD OF CANVAS INBOX
      send_email_notification(assigned_user, "Task Assigned", "You have been assigned: \"#{title}\"", "assigned")
      # Still add to Canvas To-Do for productivity
      add_to_canvas_todo(assigned_user) unless completed?
    else
      # No assignee - notify all group members via EMAIL
      Rails.logger.info "Sending notifications to all group members for unassigned task"
      
      group.users.find_each do |member|
        # Skip users without email channels
        unless member.email_channel.present?
          Rails.logger.warn "Skipping user #{member.id} (#{member.name}) - no email channel"
          next
        end
        
        Rails.logger.info "Sending email to #{member.name} (#{member.email})"
        send_email_notification(member, "New Task Created", "New task \"#{title}\" was created in #{group.name}", "created")
      end
      
      # Still add to everyone's To-Do
      add_to_all_members_todo unless completed?
    end
  rescue => e
    Rails.logger.error "Error in send_creation_notifications: #{e.message}"
    Rails.logger.error e.backtrace.first(5).join("\n")
    # Don't re-raise the error to prevent task creation from failing
  end
  
  def send_update_notifications
    Rails.logger.info "Task updated: #{title}"
    
    # Prevent infinite loops from completed_at/status synchronization
    return if @updating_status_completed_at
    
    # Check what changed
    changes_made = []
    changes_made << "title changed" if saved_change_to_title?
    changes_made << "description updated" if saved_change_to_description?
    changes_made << "due date changed" if saved_change_to_due_date?
    
    # Handle status changes (including completion)
    status_changed = saved_change_to_status?
    assignment_changed = saved_change_to_assigned_user_id?
    
    if status_changed
      old_status, new_status = saved_change_to_status
      handle_status_change_notifications(old_status, new_status, changes_made)
    end
    
    # Handle assignment changes
    if assignment_changed
      old_assignee_id, new_assignee_id = saved_change_to_assigned_user_id
      handle_assignment_change_notifications(old_assignee_id, new_assignee_id, changes_made)
    end
    
    # Handle legacy completed_at changes (for backward compatibility)
    # Fixed: Prevent infinite loops with flag
    if saved_change_to_completed_at? && !status_changed
      @updating_status_completed_at = true
      begin
        if completed_at.present?
          # Sync status with completed_at
          update_column(:status, 'done') if status != 'done'
        else
          # Sync status with completed_at
          update_column(:status, 'todo') if status == 'done'
        end
      ensure
        @updating_status_completed_at = false
      end
      return # Prevent further processing to avoid duplicate notifications
    end
    
    # Send general update notification if other changes were made
    if changes_made.any? && !status_changed && !assignment_changed
      message = "Task \"#{title}\" was updated: #{changes_made.join(', ')}"
      
      if assigned_user.present?
        send_email_notification(assigned_user, "Task Updated", message, "updated")
        # Update the Canvas To-Do item if it exists and task isn't completed
        update_canvas_todo(assigned_user) unless completed?
      else
        group.users.find_each do |member|
          send_email_notification(member, "Task Updated", message, "updated")
        end
      end
    end
  end
  
  def handle_status_change_notifications(old_status, new_status, changes_made)
    case new_status
    when 'done'
      # Task was completed - remove from Canvas To-Do, send EMAIL
      if assigned_user.present?
        send_email_notification(assigned_user, "Task Completed", "Good job! You completed: \"#{title}\"", "completed")
        remove_from_canvas_todo(assigned_user)
        # Notify other group members via EMAIL
        group.users.where.not(id: assigned_user.id).find_each do |member|
          send_email_notification(member, "Task Completed", "#{assigned_user.name} completed task: \"#{title}\"", "completed_by_other")
        end
      else
        # Unassigned task completed - notify everyone via EMAIL
        group.users.find_each do |member|
          send_email_notification(member, "Task Completed", "Task \"#{title}\" was completed", "completed")
        end
        remove_from_all_members_todo
      end
      changes_made << "marked as completed"
    
    when 'in_progress'
      # Task moved to in progress - send EMAIL notifications
      if assigned_user.present?
        send_email_notification(assigned_user, "Task In Progress", "Task \"#{title}\" is now in progress", "in_progress")
        group.users.where.not(id: assigned_user.id).find_each do |member|
          send_email_notification(member, "Task In Progress", "#{assigned_user.name} started working on: \"#{title}\"", "in_progress_by_other")
        end
      else
        group.users.find_each do |member|
          send_email_notification(member, "Task In Progress", "Task \"#{title}\" is now in progress", "in_progress")
        end
      end
      changes_made << "marked as in progress"
    
    when 'reopen'
      # Task was reopened - send EMAIL notifications
      if assigned_user.present?
        send_email_notification(assigned_user, "Task Reopened", "Task \"#{title}\" was reopened", "reopened")
        add_to_canvas_todo(assigned_user) unless completed?
        group.users.where.not(id: assigned_user.id).find_each do |member|
          send_email_notification(member, "Task Reopened", "Task \"#{title}\" was reopened and assigned to #{assigned_user.name}", "reopened_assigned")
        end
      else
        # Unassigned task reopened - notify everyone via EMAIL
        group.users.find_each do |member|
          send_email_notification(member, "Task Reopened", "Task \"#{title}\" was reopened and is available for anyone", "reopened")
        end
        add_to_all_members_todo unless completed?
      end
      changes_made << "reopened"
    
    when 'todo'
      # Task moved back to todo
      if old_status == 'done'
        # Task was uncompleted - add back to Canvas To-Do, send EMAIL
        if assigned_user.present?
          send_email_notification(assigned_user, "Task Reopened", "Task \"#{title}\" was moved back to To Do", "reopened")
          add_to_canvas_todo(assigned_user) unless completed?
        else
          group.users.find_each do |member|
            send_email_notification(member, "Task Reopened", "Task \"#{title}\" was moved back to To Do", "reopened")
          end
          add_to_all_members_todo unless completed?
        end
      else
        # Regular status change to todo - send EMAIL
        if assigned_user.present?
          send_email_notification(assigned_user, "Task Status Updated", "Task \"#{title}\" status changed to To Do", "status_updated")
        else
          group.users.find_each do |member|
            send_email_notification(member, "Task Status Updated", "Task \"#{title}\" status changed to To Do", "status_updated")
          end
        end
      end
      changes_made << "moved to To Do"
    end
  end
  
  def handle_assignment_change_notifications(old_assignee_id, new_assignee_id, changes_made)
    # Handle transition from assigned to unassigned
    if old_assignee_id.present? && new_assignee_id.nil?
      # Was assigned, now unassigned - send EMAIL notifications
      old_assignee = User.find_by(id: old_assignee_id)
      if old_assignee
        send_email_notification(old_assignee, "Task Unassigned", "You were unassigned from: \"#{title}\" - now available to all group members", "unassigned")
        remove_from_canvas_todo(old_assignee)
      end
      
      # Add to everyone's To-Do since it's now unassigned (unless completed)
      unless completed?
        add_to_all_members_todo
        group.users.find_each do |member|
          send_email_notification(member, "Task Available", "Task \"#{title}\" is now available for anyone to work on", "available")
        end
      end
    
    # Handle transition from unassigned to assigned  
    elsif old_assignee_id.nil? && new_assignee_id.present?
      # Was unassigned, now assigned - send EMAIL notifications
      remove_from_all_members_todo
      
      new_assignee = User.find_by(id: new_assignee_id)
      if new_assignee
        # Only add to To-Do if not completed
        unless completed?
          send_email_notification(new_assignee, "Task Assigned", "You were assigned: \"#{title}\"", "assigned")
          add_to_canvas_todo(new_assignee)
        else
          send_email_notification(new_assignee, "Task Assigned", "You were assigned: \"#{title}\" (already completed)", "assigned")
        end
        group.users.where.not(id: new_assignee.id).find_each do |member|
          send_email_notification(member, "Task Assigned", "Task \"#{title}\" was assigned to #{new_assignee.name}", "assigned_to_other")
        end
      end
    
    # Handle regular assignment change (from one user to another)
    elsif old_assignee_id.present? && new_assignee_id.present? && old_assignee_id != new_assignee_id
      # Remove from old assignee - send EMAIL
      old_assignee = User.find_by(id: old_assignee_id)
      if old_assignee
        send_email_notification(old_assignee, "Task Unassigned", "You were unassigned from: \"#{title}\"", "unassigned")
        remove_from_canvas_todo(old_assignee)
      end
      
      # Add to new assignee - send EMAIL
      new_assignee = User.find_by(id: new_assignee_id)
      if new_assignee
        # Only add to To-Do if not completed
        unless completed?
          send_email_notification(new_assignee, "Task Assigned", "You were assigned: \"#{title}\"", "assigned")
          add_to_canvas_todo(new_assignee)
        else
          send_email_notification(new_assignee, "Task Assigned", "You were assigned: \"#{title}\" (already completed)", "assigned")
        end
        group.users.where.not(id: new_assignee.id).find_each do |member|
          send_email_notification(member, "Task Reassigned", "Task \"#{title}\" was reassigned to #{new_assignee.name}", "reassigned")
        end
      end
    end
    
    changes_made << "assignment changed"
  end
  
  def send_deletion_notifications
    Rails.logger.info "Task deleted: #{title}"
    
    if assigned_user.present?
      send_email_notification(assigned_user, "Task Deleted", "Task \"#{title}\" was deleted", "deleted")
      remove_from_canvas_todo(assigned_user)
      group.users.where.not(id: assigned_user.id).find_each do |member|
        send_email_notification(member, "Task Deleted", "Task \"#{title}\" was deleted", "deleted")
      end
    else
      # Unassigned task deleted - notify everyone via EMAIL
      group.users.find_each do |member|
        send_email_notification(member, "Task Deleted", "Task \"#{title}\" was deleted", "deleted")
      end
      remove_from_all_members_todo
    end
  end
  
  # Email notification method replacing Canvas inbox notifications
  def send_email_notification(user, subject, message, action_type)
    # Safety checks
    return false unless user.present?
    return false unless user.email_channel.present?
    
    begin
      # Use our TaskNotificationHelper with error handling
      result = TaskNotificationHelper.send_task_email(user, subject, message, self, action_type)
      
      if result
        Rails.logger.info "Email notification sent successfully to #{user.email} for task: #{title}"
      else
        Rails.logger.warn "Email notification failed for user #{user.id} (#{user.email}) for task: #{title}"
      end
      
      return result
      
    rescue => e
      Rails.logger.error "Error sending email notification to user #{user.id}: #{e.message}"
      Rails.logger.error e.backtrace.first(3).join("\n")
      return false
    end
  end
  
  def add_to_all_members_todo
    group.users.find_each do |member|
      add_to_canvas_todo(member)
    end
  end
  
  def remove_from_all_members_todo
    group.users.find_each do |member|
      remove_from_canvas_todo(member)
    end
  end
  
  # Generate the title with [GROUP] badge
  def todo_title
    "#{title} [#{group.name}]"
  end
  
  def add_to_canvas_todo(user)
    return unless user.present?
    return if completed? # Don't add completed tasks to To-Do
    
    begin
      # Use Canvas's Planner/To-Do system
      # Canvas uses PlannerNote for user to-dos
      
      planner_note = user.planner_notes.find_or_initialize_by(
        todo_date: due_date&.to_date || Date.current,
        title: todo_title
      )
      
      planner_note.assign_attributes(
        details: description || "Group task from #{group.name}",
        course_id: group.context_id,
        workflow_state: 'active'
      )
      
      planner_note.save
      
    rescue => e
      Rails.logger.error "Error adding to Canvas To-Do: #{e.message}"
    end
  end
  
  def remove_from_canvas_todo(user)
    return unless user.present?
    
    begin
      # Remove corresponding PlannerNote - need to check for both old and new title formats
      planner_notes = user.planner_notes.where(title: [title, todo_title])
      planner_notes.destroy_all
      
    rescue => e
      Rails.logger.error "Error removing from Canvas To-Do: #{e.message}"
    end
  end
  
  def update_canvas_todo(user)
    return unless user.present?
    return if completed? # Don't update To-Do for completed tasks
    
    begin
      # Update existing PlannerNote - check for both old and new title formats
      planner_note = user.planner_notes.find_by(title: [title, todo_title])
      
      if planner_note
        planner_note.update!(
          title: todo_title,
          details: description || "Group task from #{group.name}",
          todo_date: due_date&.to_date || Date.current
        )
      else
        # If no existing note, create one
        add_to_canvas_todo(user)
      end
      
    rescue => e
      Rails.logger.error "Error updating Canvas To-Do: #{e.message}"
    end
  end
end
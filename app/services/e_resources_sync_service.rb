class EResourcesSyncService
  
  def self.sync_from_json
    new.sync_from_json
  end
  
  def self.update_json_file
    new.update_json_file
  end
  
  def self.compare_json_and_db
    new.compare_json_and_db
  end
  
  def sync_from_json
    return { success: false, message: "JSON file not found" } unless File.exist?(JSON_FILE_PATH)
    
    begin
      file_content = File.read(JSON_FILE_PATH)
      json_resources = JSON.parse(file_content)
      
      # Get all current JSON resource identifiers
      json_identifiers = json_resources.map { |data| [data["title"], data["url"]] }
      
      created_count = 0
      updated_count = 0
      removed_count = 0
      errors = []
      
      # First, remove resources that are no longer in JSON
      EResource.all.each do |db_resource|
        begin
          identifier = [db_resource.title, db_resource.url]
          unless json_identifiers.include?(identifier)
            db_resource.destroy
            removed_count += 1
          end
        rescue => e
          errors << "Error removing #{db_resource.title}: #{e.message}"
        end
      end
      
      # Then sync resources from JSON
      json_resources.each do |data|
        begin
          existing_resource = EResource.find_by(title: data["title"], url: data["url"])
          
          if existing_resource
            existing_resource.update!(
              discipline: data["discipline"],
              description: data["description"],
              tags: data["tags"],
              metadata: data["metadata"]
            )
            updated_count += 1
          else
            EResource.create!(
              title: data["title"],
              url: data["url"],
              discipline: data["discipline"],
              description: data["description"],
              tags: data["tags"],
              metadata: data["metadata"]
            )
            created_count += 1
          end
        rescue => e
          errors << "Error processing #{data['title']}: #{e.message}"
        end
      end
      
      {
        success: true,
        created: created_count,
        updated: updated_count,
        removed: removed_count,
        total: EResource.count,
        errors: errors
      }
      
    rescue JSON::ParserError => e
      { success: false, message: "JSON parsing error: #{e.message}" }
    rescue => e
      { success: false, message: "Sync error: #{e.message}" }
    end
  end
  
  # NEW METHOD: Update JSON file from database
  def update_json_file
    begin
      Rails.logger.info "Updating JSON file at: #{JSON_FILE_PATH}"
      
      # Get all resources from database
      resources = EResource.all.order(:discipline, :title).map do |resource|
        {
          title: resource.title,
          url: resource.url,
          discipline: resource.discipline,
          description: resource.description,
          tags: resource.tags || [],
          metadata: resource.metadata || {}
        }
      end
      
      Rails.logger.info "Writing #{resources.count} resources to JSON file"
      
      # Ensure directory exists
      FileUtils.mkdir_p(File.dirname(JSON_FILE_PATH))
      
      # Write to JSON file
      File.write(JSON_FILE_PATH, JSON.pretty_generate(resources))
      
      Rails.logger.info "JSON file updated successfully with #{resources.count} resources"
      
      {
        success: true,
        count: resources.count,
        message: "JSON file updated successfully"
      }
      
    rescue => e
      Rails.logger.error "Error updating JSON file: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      {
        success: false,
        message: "Error updating JSON file: #{e.message}"
      }
    end
  end
  
  def compare_json_and_db
    return { success: false, message: "JSON file not found" } unless File.exist?(JSON_FILE_PATH)
    
    begin
      file_content = File.read(JSON_FILE_PATH)
      json_resources = JSON.parse(file_content)
      
      json_titles = json_resources.map { |r| r['title'] }.sort
      db_titles = EResource.pluck(:title).sort
      
      {
        success: true,
        json_count: json_resources.count,
        db_count: EResource.count,
        json_only: json_titles - db_titles,
        db_only: db_titles - json_titles,
        in_sync: (json_titles - db_titles).empty? && (db_titles - json_titles).empty?
      }
      
    rescue JSON::ParserError => e
      { success: false, message: "JSON parsing error: #{e.message}" }
    rescue => e
      { success: false, message: "Comparison error: #{e.message}" }
    end
  end
end


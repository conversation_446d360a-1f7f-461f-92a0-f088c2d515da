# frozen_string_literal: true

module TaskNotificationHelper
  # Sends a task notification email to a user
  # @param [User] user The user to send the email to
  # @param [String] subject The email subject
  # @param [String] message The main message content
  # @param [GroupTask] task The task object
  # @param [String] action_type The type of action (created, assigned, completed, etc.)
  def self.send_task_email(user, subject, message, task, action_type)
    return false unless user.email_channel
    
    begin
      # Build the task URL for the email using dynamic domain configuration
      config = ConfigFile.load('domain')
      domain = config['domain']
      task_url = "http://#{domain}/groups/#{task.group.id}/tasks"
      
      # Create the email message using the same pattern as ReportNotificationHelper
      m = user.email_channel.messages.temp_record
      m.to = user.email_channel.path
      m.context = task.group
      m.user = user
      m.subject = subject
      
      # Build email body with task details
      m.body = build_task_email_body(message, task, task_url, action_type)
      
      # Create or find notification type for tasks
      m.notification = Notification.create_or_find_by(
        name: 'group_task_notification', 
        category: 'Group Tasks'
      )
      
      m.parse!('email')
      message_obj = Mailer.create_message(m)
      Mailer.deliver(message_obj)
      
      Rails.logger.info "Task email sent to #{user.email} for task: #{task.title}"
      true
      
    rescue Net::OpenTimeout, Net::ReadTimeout => e
      # Handle SMTP timeouts gracefully - email was created successfully
      Rails.logger.warn "SMTP timeout when sending task email to #{user.email}, but email was processed: #{e.message}"
      true # Return true because the email was processed, just delivery timed out
      
    rescue => e
      Rails.logger.error("Failed to send task notification email to #{user.email}: #{e.message}")
      false
    end
  end
  
  private
  
  # Builds the email body with task details and formatting
  def self.build_task_email_body(message, task, task_url, action_type)
    due_date_text = task.due_date.present? ? task.due_date.strftime('%B %d, %Y at %I:%M %p') : "No due date set"
    assigned_to_text = task.assigned_user.present? ? task.assigned_user.name : "Unassigned"
    status_text = task.status_display
    
    <<~BODY
      Hello #{task.assigned_user&.name || 'Team Member'},

      #{message}

      Task Details:
      • Title: #{task.title}
      • Description: #{task.description.present? ? task.description : 'No description provided'}
      • Status: #{status_text}
      • Assigned to: #{assigned_to_text}
      • Due date: #{due_date_text}
      • Group: #{task.group.name}

      You can view and manage this task by visiting:
      #{task_url}

      ---
      This is an automated notification from your RSU-LMS group task system.
      Time sent: #{Time.current.strftime("%Y-%m-%d %H:%M:%S %Z")}
    BODY
  end
end
class TaskNotificationJob < ActiveJob::Base
  queue_as :default
  
  def perform(action, task_data)
    case action
    when 'create'
      handle_creation_notifications(task_data)
    when 'update'  
      handle_update_notifications(task_data)
    when 'destroy'
      handle_deletion_notifications(task_data)
    end
  rescue => e
    Rails.logger.error "TaskNotificationJob failed for #{action}: #{e.message}"
    # Don't re-raise - let the job fail gracefully
  end
  
  private
  
  def handle_creation_notifications(task_data)
    group = Group.find(task_data[:group_id])
    assigned_user = User.find_by(id: task_data[:assigned_user_id])
    
    Rails.logger.info "Background job: Task created: #{task_data[:title]}"
    
    if assigned_user.present?
      # Notify only the assigned user
      TaskNotificationHelper.send_task_email(
        assigned_user, 
        "Task Assigned", 
        "You have been assigned: \"#{task_data[:title]}\"", 
        OpenStruct.new(task_data.merge(group: group)), 
        "assigned"
      )
      # Add to Canvas To-Do for productivity
      add_to_canvas_todo(assigned_user, task_data) unless task_data[:completed]
    else
      # No assignee - notify all group members
      group.users.preload(:communication_channels).find_each do |member|
        next unless member.communication_channels.present?
        
        TaskNotificationHelper.send_task_email(
          member, 
          "New Task Created", 
          "New task \"#{task_data[:title]}\" was created in #{group.name}", 
          OpenStruct.new(task_data.merge(group: group)), 
          "created"
        )
      end
      
      # Add to everyone's To-Do
      add_to_all_members_todo(group, task_data) unless task_data[:completed]
    end
  end
  
def handle_update_notifications(task_data)
    group = Group.find(task_data[:group_id])
    assigned_user = User.find_by(id: task_data[:assigned_user_id])
    changes = task_data[:changes]
    old_values = task_data[:old_values]
    
    Rails.logger.info "Background job: Task updated: #{task_data[:title]}"
    
    changes_made = []
    changes_made << "title changed" if changes[:title]
    changes_made << "description updated" if changes[:description]
    changes_made << "due date changed" if changes[:due_date]
    
    # Handle status changes
    if changes[:status]
      handle_status_change_notifications(old_values[:status], task_data, group, assigned_user, changes_made)
    end
    
    # Handle assignment changes  
    if changes[:assigned_user_id]
      handle_assignment_change_notifications(old_values[:assigned_user_id], task_data, group, changes_made)
    end
    
    # Send general update notification if other changes were made
    if changes_made.any? && !changes[:status] && !changes[:assigned_user_id]
      message = "Task \"#{task_data[:title]}\" was updated: #{changes_made.join(', ')}"
      
      if assigned_user.present?
        TaskNotificationHelper.send_task_email(
          assigned_user, 
          "Task Updated", 
          message, 
          OpenStruct.new(task_data.merge(group: group)), 
          "updated"
        )
      else
        group.users.preload(:communication_channels).find_each do |member|
          next unless member.communication_channels.present?
          
          TaskNotificationHelper.send_task_email(
            member, 
            "Task Updated", 
            message, 
            OpenStruct.new(task_data.merge(group: group)), 
            "updated"
          )
        end
      end
    end
  end
  
  def handle_deletion_notifications(task_data)
    group = Group.find(task_data[:group_id])
    assigned_user = User.find_by(id: task_data[:assigned_user_id])
    
    Rails.logger.info "Background job: Task deleted: #{task_data[:title]}"
    
    if assigned_user.present?
      TaskNotificationHelper.send_task_email(
        assigned_user, 
        "Task Deleted", 
        "Task \"#{task_data[:title]}\" was deleted", 
        OpenStruct.new(task_data.merge(group: group)), 
        "deleted"
      )
      
      group.users.preload(:communication_channels).where.not(id: assigned_user.id).find_each do |member|
        next unless member.communication_channels.present?
        
        TaskNotificationHelper.send_task_email(
          member, 
          "Task Deleted", 
          "Task \"#{task_data[:title]}\" was deleted", 
          OpenStruct.new(task_data.merge(group: group)), 
          "deleted"
        )
      end
    else
      # Unassigned task deleted - notify everyone
      group.users.preload(:communication_channels).find_each do |member|
        next unless member.communication_channels.present?
        
        TaskNotificationHelper.send_task_email(
          member, 
          "Task Deleted", 
          "Task \"#{task_data[:title]}\" was deleted", 
          OpenStruct.new(task_data.merge(group: group)), 
          "deleted"
        )
      end
    end
  end
  
  def handle_status_change_notifications(old_status, task_data, group, assigned_user, changes_made)
    # Get current status from the task data
    current_status = GroupTask.find(task_data[:id]).status
    
    case current_status
    when 'done'
      # Task was completed - remove from Canvas To-Do, send EMAIL
      if assigned_user.present?
        TaskNotificationHelper.send_task_email(
          assigned_user, 
          "Task Completed", 
          "Good job! You completed: \"#{task_data[:title]}\"", 
          OpenStruct.new(task_data.merge(group: group)), 
          "completed"
        )
        remove_from_canvas_todo(assigned_user, task_data)
        
        # Notify other group members
        group.users.preload(:communication_channels).where.not(id: assigned_user.id).find_each do |member|
          next unless member.communication_channels.present?
          
          TaskNotificationHelper.send_task_email(
            member, 
            "Task Completed", 
            "#{assigned_user.name} completed task: \"#{task_data[:title]}\"", 
            OpenStruct.new(task_data.merge(group: group)), 
            "completed_by_other"
          )
        end
      else
        # Unassigned task completed
        group.users.preload(:communication_channels).find_each do |member|
          next unless member.communication_channels.present?
          
          TaskNotificationHelper.send_task_email(
            member, 
            "Task Completed", 
            "Task \"#{task_data[:title]}\" was completed", 
            OpenStruct.new(task_data.merge(group: group)), 
            "completed"
          )
        end
        remove_from_all_members_todo(group, task_data)
      end
      changes_made << "marked as completed"
    
    when 'in_progress'
      # Task moved to in progress - send EMAIL notifications
      if assigned_user.present?
        TaskNotificationHelper.send_task_email(
          assigned_user, 
          "Task In Progress", 
          "Task \"#{task_data[:title]}\" is now in progress", 
          OpenStruct.new(task_data.merge(group: group)), 
          "in_progress"
        )
        
        group.users.preload(:communication_channels).where.not(id: assigned_user.id).find_each do |member|
          next unless member.communication_channels.present?
          
          TaskNotificationHelper.send_task_email(
            member, 
            "Task In Progress", 
            "#{assigned_user.name} started working on: \"#{task_data[:title]}\"", 
            OpenStruct.new(task_data.merge(group: group)), 
            "in_progress_by_other"
          )
        end
      else
        group.users.preload(:communication_channels).find_each do |member|
          next unless member.communication_channels.present?
          
          TaskNotificationHelper.send_task_email(
            member, 
            "Task In Progress", 
            "Task \"#{task_data[:title]}\" is now in progress", 
            OpenStruct.new(task_data.merge(group: group)), 
            "in_progress"
          )
        end
      end
      changes_made << "marked as in progress"
    
    when 'reopen'
      # Task was reopened - send EMAIL notifications
      if assigned_user.present?
        TaskNotificationHelper.send_task_email(
          assigned_user, 
          "Task Reopened", 
          "Task \"#{task_data[:title]}\" was reopened", 
          OpenStruct.new(task_data.merge(group: group)), 
          "reopened"
        )
        add_to_canvas_todo(assigned_user, task_data) unless task_data[:completed]
        
        group.users.preload(:communication_channels).where.not(id: assigned_user.id).find_each do |member|
          next unless member.communication_channels.present?
          
          TaskNotificationHelper.send_task_email(
            member, 
            "Task Reopened", 
            "Task \"#{task_data[:title]}\" was reopened and assigned to #{assigned_user.name}", 
            OpenStruct.new(task_data.merge(group: group)), 
            "reopened_assigned"
          )
        end
      else
        # Unassigned task reopened - notify everyone via EMAIL
        group.users.preload(:communication_channels).find_each do |member|
          next unless member.communication_channels.present?
          
          TaskNotificationHelper.send_task_email(
            member, 
            "Task Reopened", 
            "Task \"#{task_data[:title]}\" was reopened and is available for anyone", 
            OpenStruct.new(task_data.merge(group: group)), 
            "reopened"
          )
        end
        add_to_all_members_todo(group, task_data) unless task_data[:completed]
      end
      changes_made << "reopened"
    
    when 'todo'
      # Task moved back to todo
      if old_status == 'done'
        # Task was uncompleted - add back to Canvas To-Do, send EMAIL
        if assigned_user.present?
          TaskNotificationHelper.send_task_email(
            assigned_user, 
            "Task Reopened", 
            "Task \"#{task_data[:title]}\" was moved back to To Do", 
            OpenStruct.new(task_data.merge(group: group)), 
            "reopened"
          )
          add_to_canvas_todo(assigned_user, task_data) unless task_data[:completed]
        else
          group.users.preload(:communication_channels).find_each do |member|
            next unless member.communication_channels.present?
            
            TaskNotificationHelper.send_task_email(
              member, 
              "Task Reopened", 
              "Task \"#{task_data[:title]}\" was moved back to To Do", 
              OpenStruct.new(task_data.merge(group: group)), 
              "reopened"
            )
          end
          add_to_all_members_todo(group, task_data) unless task_data[:completed]
        end
      else
        # Regular status change to todo - send EMAIL
        if assigned_user.present?
          TaskNotificationHelper.send_task_email(
            assigned_user, 
            "Task Status Updated", 
            "Task \"#{task_data[:title]}\" status changed to To Do", 
            OpenStruct.new(task_data.merge(group: group)), 
            "status_updated"
          )
        else
          group.users.preload(:communication_channels).find_each do |member|
            next unless member.communication_channels.present?
            
            TaskNotificationHelper.send_task_email(
              member, 
              "Task Status Updated", 
              "Task \"#{task_data[:title]}\" status changed to To Do", 
              OpenStruct.new(task_data.merge(group: group)), 
              "status_updated"
            )
          end
        end
      end
      changes_made << "moved to To Do"
    end
  end
  
  def handle_assignment_change_notifications(old_assignee_id, task_data, group, changes_made)
    new_assignee_id = task_data[:assigned_user_id]
    
    # Handle transition from assigned to unassigned
    if old_assignee_id.present? && new_assignee_id.nil?
      old_assignee = User.find_by(id: old_assignee_id)
      if old_assignee
        TaskNotificationHelper.send_task_email(
          old_assignee, 
          "Task Unassigned", 
          "You were unassigned from: \"#{task_data[:title]}\" - now available to all group members", 
          OpenStruct.new(task_data.merge(group: group)), 
          "unassigned"
        )
        remove_from_canvas_todo(old_assignee, task_data)
      end
      
      # Add to everyone's To-Do since it's now unassigned (unless completed)
      unless task_data[:completed]
        add_to_all_members_todo(group, task_data)
        group.users.preload(:communication_channels).find_each do |member|
          next unless member.communication_channels.present?
          
          TaskNotificationHelper.send_task_email(
            member, 
            "Task Available", 
            "Task \"#{task_data[:title]}\" is now available for anyone to work on", 
            OpenStruct.new(task_data.merge(group: group)), 
            "available"
          )
        end
      end
    
    # Handle transition from unassigned to assigned  
    elsif old_assignee_id.nil? && new_assignee_id.present?
      # Was unassigned, now assigned - remove from everyone's To-Do
      remove_from_all_members_todo(group, task_data)
      
      new_assignee = User.find_by(id: new_assignee_id)
      if new_assignee
        # Only add to To-Do if not completed
        unless task_data[:completed]
          TaskNotificationHelper.send_task_email(
            new_assignee, 
            "Task Assigned", 
            "You were assigned: \"#{task_data[:title]}\"", 
            OpenStruct.new(task_data.merge(group: group)), 
            "assigned"
          )
          add_to_canvas_todo(new_assignee, task_data)
        else
          TaskNotificationHelper.send_task_email(
            new_assignee, 
            "Task Assigned", 
            "You were assigned: \"#{task_data[:title]}\" (already completed)", 
            OpenStruct.new(task_data.merge(group: group)), 
            "assigned"
          )
        end
        
        group.users.preload(:communication_channels).where.not(id: new_assignee.id).find_each do |member|
          next unless member.communication_channels.present?
          
          TaskNotificationHelper.send_task_email(
            member, 
            "Task Assigned", 
            "Task \"#{task_data[:title]}\" was assigned to #{new_assignee.name}", 
            OpenStruct.new(task_data.merge(group: group)), 
            "assigned_to_other"
          )
        end
      end
    
    # Handle regular assignment change (from one user to another)
    elsif old_assignee_id.present? && new_assignee_id.present? && old_assignee_id != new_assignee_id
      # Remove from old assignee
      old_assignee = User.find_by(id: old_assignee_id)
      if old_assignee
        TaskNotificationHelper.send_task_email(
          old_assignee, 
          "Task Unassigned", 
          "You were unassigned from: \"#{task_data[:title]}\"", 
          OpenStruct.new(task_data.merge(group: group)), 
          "unassigned"
        )
        remove_from_canvas_todo(old_assignee, task_data)
      end
      
      # Add to new assignee
      new_assignee = User.find_by(id: new_assignee_id)
      if new_assignee
        # Only add to To-Do if not completed
        unless task_data[:completed]
          TaskNotificationHelper.send_task_email(
            new_assignee, 
            "Task Assigned", 
            "You were assigned: \"#{task_data[:title]}\"", 
            OpenStruct.new(task_data.merge(group: group)), 
            "assigned"
          )
          add_to_canvas_todo(new_assignee, task_data)
        else
          TaskNotificationHelper.send_task_email(
            new_assignee, 
            "Task Assigned", 
            "You were assigned: \"#{task_data[:title]}\" (already completed)", 
            OpenStruct.new(task_data.merge(group: group)), 
            "assigned"
          )
        end
        
        group.users.preload(:communication_channels).where.not(id: new_assignee.id).find_each do |member|
          next unless member.communication_channels.present?
          
          TaskNotificationHelper.send_task_email(
            member, 
            "Task Reassigned", 
            "Task \"#{task_data[:title]}\" was reassigned to #{new_assignee.name}", 
            OpenStruct.new(task_data.merge(group: group)), 
            "reassigned"
          )
        end
      end
    end
    
    changes_made << "assignment changed"
  end

# Add these methods at the bottom of your TaskNotificationJob class

def add_to_canvas_todo(user, task_data)
  return unless user.present?
  return if task_data[:completed]
  
  begin
    group = Group.find(task_data[:group_id])
    
    planner_note = user.planner_notes.find_or_initialize_by(
      todo_date: task_data[:due_date]&.to_date || Date.current,
      title: todo_title(task_data, group.name)
    )
    
    planner_note.assign_attributes(
      details: task_data[:description] || "Group task from #{group.name}",
      course_id: group.context_id,
      workflow_state: 'active'
    )
    
    planner_note.save
    Rails.logger.info "Added to Canvas To-Do for #{user.name}: #{task_data[:title]}"
    
  rescue => e
    Rails.logger.error "Error adding to Canvas To-Do: #{e.message}"
  end
end

def remove_from_canvas_todo(user, task_data)
  return unless user.present?
  
  begin
    group = Group.find(task_data[:group_id])
    planner_notes = user.planner_notes.where(title: [task_data[:title], todo_title(task_data, group.name)])
    planner_notes.destroy_all
    Rails.logger.info "Removed from Canvas To-Do for #{user.name}: #{task_data[:title]}"
    
  rescue => e
    Rails.logger.error "Error removing from Canvas To-Do: #{e.message}"
  end
end

def update_canvas_todo(user, task_data)
  return unless user.present?
  return if task_data[:completed]
  
  begin
    group = Group.find(task_data[:group_id])
    planner_note = user.planner_notes.find_by(title: [task_data[:title], todo_title(task_data, group.name)])
    
    if planner_note
      planner_note.update!(
        title: todo_title(task_data, group.name),
        details: task_data[:description] || "Group task from #{group.name}",
        todo_date: task_data[:due_date]&.to_date || Date.current
      )
      Rails.logger.info "Updated Canvas To-Do for #{user.name}: #{task_data[:title]}"
    else
      add_to_canvas_todo(user, task_data)
    end
    
  rescue => e
    Rails.logger.error "Error updating Canvas To-Do: #{e.message}"
  end
end

def todo_title(task_data, group_name = nil)
  group_name ||= Group.find(task_data[:group_id]).name
  "#{task_data[:title]} [#{group_name}]"
end

def add_to_all_members_todo(group, task_data)
  group.users.find_each do |member|
    add_to_canvas_todo(member, task_data)
  end
end

def remove_from_all_members_todo(group, task_data)
  group.users.find_each do |member|
    remove_from_canvas_todo(member, task_data)
  end
end

end